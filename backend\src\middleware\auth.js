const jwt = require('jsonwebtoken');
const User = require('../models/User');
const logger = require('../utils/logger');

// JWT 認證中間件
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供認證令牌',
      });
    }

    // 驗證 Token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 查找用戶
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '無效的認證令牌',
      });
    }

    // 檢查用戶狀態
    if (!user.isActive) {
      return res.status(403).json({
        success: false,
        message: '帳戶已被停用',
      });
    }

    // 將用戶資訊添加到請求對象
    req.user = {
      id: user._id,
      username: user.username,
      email: user.email,
      walletAddress: user.walletAddress,
      walletConnected: user.walletConnected,
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: '無效的認證令牌',
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '認證令牌已過期',
      });
    }

    logger.error('認證中間件錯誤:', error);
    return res.status(500).json({
      success: false,
      message: '認證過程中發生錯誤',
    });
  }
};

// 可選認證中間件（不強制要求認證）
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId);

    if (user && user.isActive) {
      req.user = {
        id: user._id,
        username: user.username,
        email: user.email,
        walletAddress: user.walletAddress,
        walletConnected: user.walletConnected,
      };
    } else {
      req.user = null;
    }

    next();
  } catch (error) {
    // 可選認證失敗時不返回錯誤，只是設置 user 為 null
    req.user = null;
    next();
  }
};

// 檢查錢包連接狀態
const requireWalletConnection = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: '需要用戶認證',
    });
  }

  if (!req.user.walletConnected || !req.user.walletAddress) {
    return res.status(403).json({
      success: false,
      message: '需要連接 Solana 錢包',
    });
  }

  next();
};

// 管理員權限檢查（預留功能）
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '需要用戶認證',
      });
    }

    const user = await User.findById(req.user.id);
    
    // TODO: 添加管理員角色檢查邏輯
    // if (!user.isAdmin) {
    //   return res.status(403).json({
    //     success: false,
    //     message: '需要管理員權限',
    //   });
    // }

    next();
  } catch (error) {
    logger.error('管理員權限檢查錯誤:', error);
    return res.status(500).json({
      success: false,
      message: '權限檢查過程中發生錯誤',
    });
  }
};

module.exports = {
  authenticateToken,
  optionalAuth,
  requireWalletConnection,
  requireAdmin,
};
