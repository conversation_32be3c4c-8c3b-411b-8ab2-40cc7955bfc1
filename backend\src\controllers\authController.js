const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { solanaService } = require('../services/solanaService');
const logger = require('../utils/logger');

// 生成 JWT Token
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
  );
};

// 用戶註冊
const register = async (req, res, next) => {
  try {
    const { username, email, password } = req.body;

    // 驗證必要欄位
    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        message: '請提供用戶名、電子郵件和密碼',
      });
    }

    // 檢查用戶是否已存在
    const existingUser = await User.findOne({
      $or: [{ email }, { username }],
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: '用戶名或電子郵件已存在',
      });
    }

    // 創建新用戶
    const user = new User({
      username,
      email,
      password,
    });

    await user.save();

    // 生成 Token
    const token = generateToken(user._id);

    logger.info(`新用戶註冊成功: ${username} (${email})`);

    res.status(201).json({
      success: true,
      message: '註冊成功',
      data: {
        user: user.getPublicProfile(),
        token,
      },
    });
  } catch (error) {
    logger.error('用戶註冊失敗:', error);
    next(error);
  }
};

// 用戶登入
const login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // 驗證必要欄位
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: '請提供電子郵件和密碼',
      });
    }

    // 查找用戶
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '電子郵件或密碼錯誤',
      });
    }

    // 驗證密碼
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '電子郵件或密碼錯誤',
      });
    }

    // 檢查帳戶狀態
    if (!user.isActive) {
      return res.status(403).json({
        success: false,
        message: '帳戶已被停用',
      });
    }

    // 更新最後登入時間
    await user.updateLastLogin();

    // 生成 Token
    const token = generateToken(user._id);

    logger.info(`用戶登入成功: ${user.username} (${email})`);

    res.json({
      success: true,
      message: '登入成功',
      data: {
        user: user.getPublicProfile(),
        token,
      },
    });
  } catch (error) {
    logger.error('用戶登入失敗:', error);
    next(error);
  }
};

// 錢包連接
const connectWallet = async (req, res, next) => {
  try {
    const { walletAddress, signature, message } = req.body;
    const userId = req.user.id; // 從認證中間件獲取

    // 驗證必要欄位
    if (!walletAddress || !signature || !message) {
      return res.status(400).json({
        success: false,
        message: '請提供錢包地址、簽名和訊息',
      });
    }

    // 驗證錢包地址格式
    if (!solanaService.isValidPublicKey(walletAddress)) {
      return res.status(400).json({
        success: false,
        message: '無效的錢包地址格式',
      });
    }

    // 驗證簽名
    const isSignatureValid = await solanaService.verifySignature(
      message,
      signature,
      walletAddress
    );

    if (!isSignatureValid) {
      return res.status(401).json({
        success: false,
        message: '簽名驗證失敗',
      });
    }

    // 檢查錢包是否已被其他用戶使用
    const existingWallet = await User.findOne({
      walletAddress,
      _id: { $ne: userId },
    });

    if (existingWallet) {
      return res.status(409).json({
        success: false,
        message: '此錢包已被其他用戶連接',
      });
    }

    // 更新用戶錢包資訊
    const user = await User.findByIdAndUpdate(
      userId,
      {
        walletAddress,
        walletConnected: true,
      },
      { new: true }
    );

    logger.info(`用戶 ${user.username} 成功連接錢包: ${walletAddress}`);

    res.json({
      success: true,
      message: '錢包連接成功',
      data: {
        user: user.getPublicProfile(),
        walletAddress,
      },
    });
  } catch (error) {
    logger.error('錢包連接失敗:', error);
    next(error);
  }
};

// 斷開錢包連接
const disconnectWallet = async (req, res, next) => {
  try {
    const userId = req.user.id;

    const user = await User.findByIdAndUpdate(
      userId,
      {
        walletAddress: null,
        walletConnected: false,
      },
      { new: true }
    );

    logger.info(`用戶 ${user.username} 斷開錢包連接`);

    res.json({
      success: true,
      message: '錢包連接已斷開',
      data: {
        user: user.getPublicProfile(),
      },
    });
  } catch (error) {
    logger.error('斷開錢包連接失敗:', error);
    next(error);
  }
};

// 獲取當前用戶資訊
const getCurrentUser = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用戶不存在',
      });
    }

    res.json({
      success: true,
      message: '獲取用戶資訊成功',
      data: {
        user: user.getPublicProfile(),
      },
    });
  } catch (error) {
    logger.error('獲取用戶資訊失敗:', error);
    next(error);
  }
};

module.exports = {
  register,
  login,
  connectWallet,
  disconnectWallet,
  getCurrentUser,
};
