# browserify-aes
[![Build Status](https://travis-ci.org/crypto-browserify/browserify-aes.svg)](https://travis-ci.org/crypto-browserify/browserify-aes)

Node style aes for use in the browser.
Implements:

 - createCipher
 - createCipheriv
 - createDecipher
 - createDecipheriv
 - getCiphers

In node.js, the `crypto` implementation is used, in browsers it falls back to a pure JavaScript implementation.

Much of this library has been taken from the aes implementation in [triplesec](https://github.com/keybase/triplesec),  a partial derivation of [crypto-js](https://code.google.com/p/crypto-js/).

`EVP_BytesToKey` is a straight up port of the same function from OpenSSL as there is literally no documenation on it beyond it using 'undocumented extensions' for longer keys.

## LICENSE [MIT](LICENSE)
