{"name": "chainxiang-backend", "version": "1.0.0", "description": "ChainXiang Backend - Solana區塊鏈象棋App後端服務", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "dependencies": {"@solana/web3.js": "^1.98.2", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "bs58": "^5.0.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.6.3", "morgan": "^1.10.0", "redis": "^4.6.10", "socket.io": "^4.7.4", "tweetnacl": "^1.0.3", "winston": "^3.11.0"}, "devDependencies": {"eslint": "^8.52.0", "eslint-config-node": "^4.1.0", "jest": "^29.7.0", "mongodb-memory-server": "^9.0.1", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["solana", "blockchain", "chess", "game", "websocket", "nodejs"], "author": "ChainXiang Development Team", "license": "MIT"}