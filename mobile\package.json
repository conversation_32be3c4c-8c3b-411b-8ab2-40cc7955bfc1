{"name": "chainxiang-mobile", "version": "1.0.0", "description": "ChainXiang - Solana區塊鏈象棋App", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "@react-native-async-storage/async-storage": "^1.19.5", "react-native-keychain": "^8.1.3", "react-native-elements": "^3.4.3", "react-native-vector-icons": "^10.0.2", "react-native-reanimated": "^3.5.4", "react-native-gesture-handler": "^2.13.4", "react-native-safe-area-context": "^4.7.4", "react-native-screens": "^3.27.0", "socket.io-client": "^4.7.4", "react-native-sound": "^0.11.2", "react-native-linear-gradient": "^2.8.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/preset-react": "^7.18.6", "@babel/runtime": "^7.20.0", "@babel/eslint-parser": "^7.19.1", "@react-native/eslint-config": "^0.72.0", "@react-native/metro-config": "^0.72.0", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "eslint-plugin-react": "^7.31.10", "eslint-plugin-react-native": "^4.0.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}