const express = require('express');
const router = express.Router();
const {
  register,
  login,
  connectWallet,
  disconnectWallet,
  getCurrentUser,
} = require('../controllers/authController');
const { authenticateToken } = require('../middleware/auth');

// 用戶註冊
router.post('/register', register);

// 用戶登入
router.post('/login', login);

// 獲取當前用戶資訊（需要認證）
router.get('/me', authenticateToken, getCurrentUser);

// 錢包連接（需要認證）
router.post('/wallet-connect', authenticateToken, connectWallet);

// 斷開錢包連接（需要認證）
router.post('/wallet-disconnect', authenticateToken, disconnectWallet);

// 用戶登出（客戶端處理，服務端無需特殊邏輯）
router.post('/logout', (req, res) => {
  res.json({
    success: true,
    message: '登出成功',
    data: null,
  });
});

module.exports = router;
