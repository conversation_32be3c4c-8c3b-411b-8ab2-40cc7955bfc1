SJCL used to be in the public domain.  Now it's:

Copyright 2009-2010 <PERSON>, <PERSON>, <PERSON>, Stanford University.

This is for liability reasons.  (Speaking of which, SJCL comes with NO
WARRANTY WHATSOEVER, express or implied, to the limit of applicable
law.)

SJCL is dual-licensed under the GNU GPL version 2.0 or higher, and a
2-clause BSD license.  You may use SJCL under the terms of either of
these licenses.  For your convenience, the GPL versions 2.0 and 3.0
and the 2-clause BSD license are included here.  Additionally, you may
serve "crunched" copies of sjcl (i.e. those with comments removed,
and other transformations to reduce code size) without any copyright
notice.

SJCL includes JsDoc toolkit, YUI compressor, Closure compressor,
JSLint and the CodeView template in its build system.  These programs'
copyrights are owned by other people.  They are distributed here under
the MPL, MIT, BSD, Apache and JSLint licenses.  Codeview is "free for
download" but has no license attached; it is Copyright 2010 Wouter <PERSON>s.

The BSD license is (almost?) strictly more permissive, but the
additionally licensing under the GPL allows us to use OCB 2.0 code
royalty-free (at least, if OCB 2.0's creator <PERSON> has anything
to say about it).  Note that if you redistribute SJCL under a license
other than the GPL, you or your users may need to pay patent licensing
fees for OCB 2.0.

There may be patents which apply to SJCL other than Phil Rogaway's OCB
patents.  We suggest that you consult legal counsel before using SJCL
in a commercial project.

-----

Please note, two Java JAR files, Google Closure Compiler and 
YUI Compressor, are provided in the "compress" folder as a convenience 
for the compiling process of SJCL.  These are not part of SJCL itself 
and provided under their own licenses.

As of October 2015, more information can be found at the following
locations:

Google Closure Compiler
  -  https://developers.google.com/closure/compiler/

YUI Compressor
  -  http://yui.github.io/yuicompressor/

