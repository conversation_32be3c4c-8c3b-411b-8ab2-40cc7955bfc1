const logger = require('../utils/logger');

// 匹配 Socket 事件處理器
function matchSocket(io, socket) {
  // 開始匹配
  socket.on('start-matching', (data) => {
    try {
      const { userId, gameMode } = data;
      logger.info(`用戶 ${userId} 開始匹配，遊戲模式: ${gameMode}`);
      
      // TODO: 實現匹配邏輯
      
      socket.emit('matching-started', {
        success: true,
        message: '開始匹配',
        userId,
        gameMode,
      });
      
    } catch (error) {
      logger.error('開始匹配錯誤:', error);
      socket.emit('matching-error', {
        success: false,
        message: '匹配失敗',
        error: error.message,
      });
    }
  });

  // 取消匹配
  socket.on('cancel-matching', (data) => {
    try {
      const { userId } = data;
      logger.info(`用戶 ${userId} 取消匹配`);
      
      // TODO: 實現取消匹配邏輯
      
      socket.emit('matching-cancelled', {
        success: true,
        message: '已取消匹配',
        userId,
      });
      
    } catch (error) {
      logger.error('取消匹配錯誤:', error);
    }
  });

  // 匹配成功處理
  socket.on('match-found', (data) => {
    try {
      const { roomId, players } = data;
      logger.info(`匹配成功，房間: ${roomId}，玩家:`, players);
      
      // TODO: 實現匹配成功後的房間創建邏輯
      
      // 通知匹配成功的玩家
      players.forEach(playerId => {
        io.to(playerId).emit('match-found', {
          success: true,
          roomId,
          players,
          message: '匹配成功！',
        });
      });
      
    } catch (error) {
      logger.error('匹配成功處理錯誤:', error);
    }
  });
}

module.exports = matchSocket;
