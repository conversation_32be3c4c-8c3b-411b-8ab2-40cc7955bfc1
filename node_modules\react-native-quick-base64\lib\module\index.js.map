{"version": 3, "names": ["NativeModules", "Base64Module", "QuickBase64", "global", "base64FromArrayBuffer", "install", "getLens", "b64", "len", "length", "Error", "validLen", "indexOf", "placeHoldersLen", "uint8ArrayToString", "array", "out", "i", "charCode", "undefined", "String", "fromCharCode", "stringToArrayBuffer", "str", "buf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON><PERSON>", "Uint8Array", "charCodeAt", "byteLength", "toByteArray", "removeLinebreaks", "base64ToArrayBuffer", "fromByteArray", "uint8", "urlSafe", "buffer", "byteOffset", "slice", "btoa", "data", "atob", "shim", "getNative", "trimBase64Padding", "replace"], "sourceRoot": "../../src", "sources": ["index.ts"], "mappings": ";;AAAA,SAASA,aAAa,QAAQ,cAAc;AAE5C,MAAMC,YAAY,GAAGD,aAAa,CAACE,WAAW;AAE9C,IAAID,YAAY,IAAI,OAAOE,MAAM,CAACC,qBAAqB,KAAK,UAAU,EAAE;EACtEH,YAAY,CAACI,OAAO,CAAC,CAAC;AACxB;;AAEA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,GAAW,EAAE;EAC5B,MAAMC,GAAG,GAAGD,GAAG,CAACE,MAAM;EAEtB,IAAID,GAAG,GAAG,CAAC,GAAG,CAAC,EAAE;IACf,MAAM,IAAIE,KAAK,CAAC,gDAAgD,CAAC;EACnE;EAEA,IAAIC,QAAQ,GAAGJ,GAAG,CAACK,OAAO,CAAC,GAAG,CAAC;EAC/B,IAAID,QAAQ,KAAK,CAAC,CAAC,EAAEA,QAAQ,GAAGH,GAAG;EAEnC,MAAMK,eAAe,GAAGF,QAAQ,KAAKH,GAAG,GAAG,CAAC,GAAG,CAAC,GAAIG,QAAQ,GAAG,CAAE;EAEjE,OAAO,CAACA,QAAQ,EAAEE,eAAe,CAAC;AACpC;;AAEA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,KAAiB,EAAE;EAC7C,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACN,MAAM,EAAEQ,CAAC,EAAE,EAAE;IACrC,MAAMC,QAAQ,GAAGH,KAAK,CAACE,CAAC,CAAC;IACzB,IAAIC,QAAQ,KAAKC,SAAS,EAAE;MAC1BH,GAAG,IAAII,MAAM,CAACC,YAAY,CAACH,QAAQ,CAAC;IACtC;EACF;EACA,OAAOF,GAAG;AACZ;;AAEA;AACA;AACA;AACA,SAASM,mBAAmBA,CAACC,GAAW,EAAE;EACxC,MAAMC,GAAG,GAAG,IAAIC,WAAW,CAACF,GAAG,CAACd,MAAM,CAAC;EACvC,MAAMiB,OAAO,GAAG,IAAIC,UAAU,CAACH,GAAG,CAAC;EACnC,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,GAAG,CAACd,MAAM,EAAEQ,CAAC,EAAE,EAAE;IACnCS,OAAO,CAACT,CAAC,CAAC,GAAGM,GAAG,CAACK,UAAU,CAACX,CAAC,CAAC;EAChC;EACA,OAAOO,GAAG;AACZ;;AAEA;AACA;AACA;AACA,OAAO,SAASK,UAAUA,CAACtB,GAAW,EAAE;EACtC,MAAM,CAACI,QAAQ,EAAEE,eAAe,CAAC,GAAGP,OAAO,CAACC,GAAG,CAAC;EAChD,OAAQ,CAACI,QAAQ,GAAGE,eAAe,IAAI,CAAC,GAAI,CAAC,GAAGA,eAAe;AACjE;;AAEA;AACA;AACA;AACA,OAAO,SAASiB,WAAWA,CACzBvB,GAAW,EACXwB,gBAAyB,GAAG,KAAK,EACrB;EACZ,OAAO,IAAIJ,UAAU,CAACxB,MAAM,CAAC6B,mBAAmB,CAACzB,GAAG,EAAEwB,gBAAgB,CAAC,CAAC;AAC1E;;AAEA;AACA;AACA;AACA,OAAO,SAASE,aAAaA,CAC3BC,KAAiB,EACjBC,OAAgB,GAAG,KAAK,EAChB;EACR,IAAID,KAAK,CAACE,MAAM,CAACP,UAAU,GAAGK,KAAK,CAACL,UAAU,IAAIK,KAAK,CAACG,UAAU,GAAG,CAAC,EAAE;IACtE,MAAMD,MAAM,GACVF,KAAK,CAACE,MAAM,YAAYX,WAAW,GAC/BS,KAAK,CAACE,MAAM,CAACE,KAAK,CAChBJ,KAAK,CAACG,UAAU,EAChBH,KAAK,CAACG,UAAU,GAAGH,KAAK,CAACL,UAC3B,CAAC,GACD,IAAIJ,WAAW,CAACS,KAAK,CAACL,UAAU,CAAC;IAEvC,IAAIO,MAAM,YAAYX,WAAW,EAAE;MACjC,OAAOtB,MAAM,CAACC,qBAAqB,CAACgC,MAAM,EAAED,OAAO,CAAC;IACtD;EACF;EAEA,MAAMC,MAAM,GACVF,KAAK,CAACE,MAAM,YAAYX,WAAW,GAC/BS,KAAK,CAACE,MAAM,GACZ,IAAIX,WAAW,CAACS,KAAK,CAACL,UAAU,CAAC;EACvC,OAAO1B,MAAM,CAACC,qBAAqB,CAACgC,MAAM,EAAED,OAAO,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASI,IAAIA,CAACC,IAAY,EAAE;EACjC,OAAOrC,MAAM,CAACC,qBAAqB,CAACkB,mBAAmB,CAACkB,IAAI,CAAC,EAAE,KAAK,CAAC;AACvE;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAAClC,GAAW,EAAE;EAChC,OAAOO,kBAAkB,CAACgB,WAAW,CAACvB,GAAG,CAAC,CAAC;AAC7C;;AAEA;AACA;AACA;AACA,OAAO,SAASmC,IAAIA,CAAA,EAAG;EACrBvC,MAAM,CAACoC,IAAI,GAAGA,IAAI;EAClBpC,MAAM,CAACsC,IAAI,GAAGA,IAAI;AACpB;;AAEA;AACA;AACA;AACA,OAAO,MAAME,SAAS,GAAGA,CAAA,MAAO;EAC9BvC,qBAAqB,EAAED,MAAM,CAACC,qBAAqB;EACnD4B,mBAAmB,EAAE7B,MAAM,CAAC6B;AAC9B,CAAC,CAAC;;AAEF;AACA;AACA;AACA,OAAO,MAAMY,iBAAiB,GAAIrB,GAAW,IAAK;EAChD,OAAOA,GAAG,CAACsB,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC;AACtC,CAAC", "ignoreList": []}