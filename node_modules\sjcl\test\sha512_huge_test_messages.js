// dd if=/dev/zero bs=1024 count=$((327155712/1024)) | shasum -a 512
sjcl.test.vector.sha512huge =
{
  8388608:   "cf76cca4e0f874d508f7e40fb84abc5789ca5f96c1e54e064f3be302766a59fc15a2efb7ffcc9692d13b906b2fe5a0215520d5e232ac69c754f2addb069580de", // 8 MB
  16777216:  "7e208b53e5c541b23906ef8ed8f5e12e4f1b470fbd0d3e907b1fc0c0b8d78eb1bbfb5a77dcfd9535acf6fa47f4ab956d188b770352c13b0ab7e0160690bae896", // 16 MB
  33554432:  "1aeae269f4eb7c373e3b9af7cb8eece0ada42fcd1a74ac053fc505168f9caa568cf7ccb5622b7e8ab35fad44603797be4f44efe948a7cfa3ad0381f3f875e662", // 32 MB
  67108864:  "450766d07ea8acdba4e42a47e3de22ddb35678d62ae5446832b6e3e51780ab92f365ab982152d4d63be9954770997a5438b4fb7f4db5927b9973e82dd1ce0346", // 64 MB
  134217728: "0ff7859005e5debb631f55b7dcf4fb3a1293ff937b488d8bf5a8e173d758917ccf9e835403c16db1b33d406b9b40438f88d184d95c81baece136bc68fa0ae5d2", // 128 MB
  268435456: "24078827a9a954d8be723eb76b658bf484146d67a47d6f660c72bc641e19a83e6c38099559e7ce76a9640d25f242d89f69e54fc235e1532804395aaf3fb3d671", // 256 MB
  327155712: "19042b28bdef6fa8e1894f5315fff9e1ca6f66ca80a1aff477461b62c1e31074a74c76cd619304c049a63b5a6d3784ee1c0549c36ef1618eb8210e68cc418cd1"  // 312 MB
};
