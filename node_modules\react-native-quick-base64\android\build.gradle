buildscript {
  ext.getExtOrDefault = {name ->
    return rootProject.ext.has(name) ? rootProject.ext.get(name) : project.properties['QuickBase64_' + name]
  }

  repositories {
    google()
    mavenCentral()
  }

  dependencies {
    classpath "com.android.tools.build:gradle:8.7.2"
    // noinspection DifferentKotlinGradleVersion
    classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${getExtOrDefault('kotlinVersion')}"
  }
}


apply plugin: "com.android.library"
apply plugin: "kotlin-android"

apply plugin: "com.facebook.react"

def getExtOrIntegerDefault(name) {
  return rootProject.ext.has(name) ? rootProject.ext.get(name) : (project.properties["QuickBase64_" + name]).toInteger()
}

def reactNativeArchitectures() {
  def value = project.getProperties().get("reactNativeArchitectures")
  return value ? value.split(",") : ["armeabi-v7a", "x86", "x86_64", "arm64-v8a"]
}

def resolveBuildType() {
    Gradle gradle = getGradle()
    String tskReqStr = gradle.getStartParameter().getTaskRequests()['args'].toString()

    return tskReqStr.contains('Release') ? 'release' : 'debug'
}

android {
  namespace 'com.reactnativequickbase64'

  compileSdkVersion getExtOrIntegerDefault("compileSdkVersion")

  defaultConfig {
    minSdkVersion getExtOrIntegerDefault("minSdkVersion")
    targetSdkVersion getExtOrIntegerDefault("targetSdkVersion")
    versionCode 1
    versionName "1.0"
    externalNativeBuild {
      cmake {
        cppFlags "-O2 -frtti -fexceptions -Wall -Wno-unused-variable -fstack-protector-all"
        arguments "-DANDROID_STL=c++_shared"
        abiFilters (*reactNativeArchitectures())
      }
    }
  }

  buildFeatures {
    buildConfig true
    prefab true
  }

  buildTypes {
    release {
      minifyEnabled false
    }
  }

  lintOptions {
    disable "GradleCompatible"
  }

  compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
  }

  externalNativeBuild {
    cmake {
      path "CMakeLists.txt"
    }
  }
  packagingOptions {
    doNotStrip resolveBuildType() == 'debug' ? "**/**/*.so" : ''
    excludes = [
            "META-INF",
            "META-INF/**",
            "**/libjsi.so",
            "**/libc++_shared.so"
    ]
  }

  sourceSets {
    main {
      java.srcDirs += [
        "generated/java",
        "generated/jni"
      ]
    }
  }
}

repositories {
  mavenCentral()
  google()
}

def kotlin_version = getExtOrDefault("kotlinVersion")

dependencies {
  implementation "com.facebook.react:react-android"
  implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
}

react {
  jsRootDir = file("../src/")
  libraryName = "QuickBase64"
  codegenJavaPackageName = "com.quickbase64"
}
