const logger = require('../utils/logger');

// 遊戲 Socket 事件處理器
function gameSocket(io, socket) {
  // 加入遊戲房間
  socket.on('join-game', (data) => {
    try {
      const { roomId, userId } = data;
      logger.info(`用戶 ${userId} 嘗試加入房間 ${roomId}`);
      
      // TODO: 實現加入遊戲房間邏輯
      socket.join(roomId);
      
      socket.emit('join-game-response', {
        success: true,
        message: '成功加入遊戲房間',
        roomId,
      });
      
      // 通知房間內其他玩家
      socket.to(roomId).emit('player-joined', {
        userId,
        message: '新玩家加入房間',
      });
      
    } catch (error) {
      logger.error('加入遊戲房間錯誤:', error);
      socket.emit('join-game-response', {
        success: false,
        message: '加入遊戲房間失敗',
        error: error.message,
      });
    }
  });

  // 離開遊戲房間
  socket.on('leave-game', (data) => {
    try {
      const { roomId, userId } = data;
      logger.info(`用戶 ${userId} 離開房間 ${roomId}`);
      
      socket.leave(roomId);
      
      // 通知房間內其他玩家
      socket.to(roomId).emit('player-left', {
        userId,
        message: '玩家離開房間',
      });
      
    } catch (error) {
      logger.error('離開遊戲房間錯誤:', error);
    }
  });

  // 走棋
  socket.on('make-move', (data) => {
    try {
      const { roomId, move, userId } = data;
      logger.info(`用戶 ${userId} 在房間 ${roomId} 走棋:`, move);
      
      // TODO: 實現走棋邏輯驗證
      
      // 廣播走棋給房間內其他玩家
      socket.to(roomId).emit('move-made', {
        move,
        userId,
        timestamp: new Date().toISOString(),
      });
      
      socket.emit('move-response', {
        success: true,
        message: '走棋成功',
      });
      
    } catch (error) {
      logger.error('走棋錯誤:', error);
      socket.emit('move-response', {
        success: false,
        message: '走棋失敗',
        error: error.message,
      });
    }
  });

  // 遊戲狀態同步
  socket.on('sync-game-state', (data) => {
    try {
      const { roomId } = data;
      
      // TODO: 實現遊戲狀態同步邏輯
      
      socket.emit('game-state-synced', {
        success: true,
        message: '遊戲狀態同步功能待實現',
      });
      
    } catch (error) {
      logger.error('遊戲狀態同步錯誤:', error);
    }
  });
}

module.exports = gameSocket;
