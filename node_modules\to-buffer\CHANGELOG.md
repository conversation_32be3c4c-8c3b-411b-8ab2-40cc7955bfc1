# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.1](https://github.com/browserify/to-buffer/compare/v1.2.0...v1.2.1) - 2025-06-19

### Commits

- [Fix] handle non-Uint8Arrays in node &lt; 3 [`7f8a881`](https://github.com/browserify/to-buffer/commit/7f8a881929133935f8e15ffd60d6dbbc513b2c5f)
- [Tests] add coverage [`286c96a`](https://github.com/browserify/to-buffer/commit/286c96a52cfeee14a2ba974d78071bdd667e9360)
- [Fix] provide a fallback for engines without `ArrayBuffer.isView` [`e336166`](https://github.com/browserify/to-buffer/commit/e336166b8f4bf13860bafa191ee1ec53fca2e331)
- [Fix] correct error message [`b45247e`](https://github.com/browserify/to-buffer/commit/b45247ed337fb44b2c8d74a14e8f86d985119fb9)

## [v1.2.0](https://github.com/browserify/to-buffer/compare/v1.1.1...v1.2.0) - 2025-06-17

### Commits

- [New] replace with implementation from cipher-base [`970adb5`](https://github.com/browserify/to-buffer/commit/970adb5523efdaa13f5ecb82967fc9f617865549)
- [Tests] migrate from travis to GHA [`8084393`](https://github.com/browserify/to-buffer/commit/808439337ca9ac3dbb8399079aaa2a4cb738627c)
- [eslint] fix whitespace [`a62e651`](https://github.com/browserify/to-buffer/commit/a62e651b661adf98c17e9e486b55bb8ff0ffb8c9)
- [eslint] fix semicolon usage [`4d85c63`](https://github.com/browserify/to-buffer/commit/4d85c6318c72feae8d19037937154f9b99ba266f)
- [meta] add `auto-changelog` [`aa0279c`](https://github.com/browserify/to-buffer/commit/aa0279c5199ca7fe39acfb950a4c824e97f06232)
- [readme] update URLs, add badges [`ff77d90`](https://github.com/browserify/to-buffer/commit/ff77d90b89de7b02538ecb2d6a89da086093bed8)
- [lint] switch to eslint [`e45f467`](https://github.com/browserify/to-buffer/commit/e45f467c7229e632cd3c10fc02895ba4d3204bbe)
- [Fix] validate that arrays contain valid byte values [`c2fb75e`](https://github.com/browserify/to-buffer/commit/c2fb75edf2fb113d58599e990f86574b4dfa62d8)
- [Fix] restore previous implementation Array behavior [`cb93b75`](https://github.com/browserify/to-buffer/commit/cb93b75a79caa9897f6c29ecde91f4ae35f704fe)
- [Tests] add nyc for coverage [`ab7026e`](https://github.com/browserify/to-buffer/commit/ab7026e36e3716c8101229f426e7f4571e55794b)
- [Refactor] use `safe-buffer`.from instead of `buffer-from` [`8e01307`](https://github.com/browserify/to-buffer/commit/8e01307191245044469e47695c5c2675b85e84e9)
- [Fix] Replace Buffer.from with `buffer-from` [`d652e54`](https://github.com/browserify/to-buffer/commit/d652e54e2396a47358a553c447e0f338b4c2dc67)
- [Tests] use `deepEqual` over `same` alias [`66a5548`](https://github.com/browserify/to-buffer/commit/66a55480258011bb5d81c5aad1360468f418d0b4)
- [meta] add `npmignore` [`90ce602`](https://github.com/browserify/to-buffer/commit/90ce6023737d50521aff44d87063db1e3f7e352a)
- [Tests] move into a test dir, update tape [`08aea81`](https://github.com/browserify/to-buffer/commit/08aea81b61b90d1fcb7e9275b5b0ba718531d9a8)
- Only apps should have lockfiles [`16ccceb`](https://github.com/browserify/to-buffer/commit/16ccceb23f350be16e80111188c90a3492916f5d)
- [Tests] add coverage [`d2cba2e`](https://github.com/browserify/to-buffer/commit/d2cba2ec76ed43c83e2a7a91f58fa7640aeb61e9)
- [meta] update description [`2cf2a20`](https://github.com/browserify/to-buffer/commit/2cf2a200a31f9543d2e8a24b5ea0e8bd843166c3)
- [Fix] add `safe-buffer`, missing from 970adb5 [`d9a0dea`](https://github.com/browserify/to-buffer/commit/d9a0dead7c638d188f8b48cdf2b5fd6cfa886071)
- [meta] temporarily limit support to node v0.10 [`8dca458`](https://github.com/browserify/to-buffer/commit/8dca458bd9a2c6b84d6e3a1996e41b242fe1c49a)
- [meta] add missing `engines.node` [`35bdfcb`](https://github.com/browserify/to-buffer/commit/35bdfcb3a71dfbd5b35d35250c9dac19c99f4197)
- [Dev Deps] add missing peer dep [`220143f`](https://github.com/browserify/to-buffer/commit/220143f1f6e47154380c27a2d88ce300104007fa)
- [meta] add `sideEffects` flag [`cd37473`](https://github.com/browserify/to-buffer/commit/cd374738d24b22b029862b3c27b9e247d4e62daf)

## [v1.1.1](https://github.com/browserify/to-buffer/compare/v1.1.0...v1.1.1) - 2018-04-26

### Commits

- use Buffer.from when avail [`eebe20e`](https://github.com/browserify/to-buffer/commit/eebe20e0603e2c6a542b00316f1661741fdf1124)

## [v1.1.0](https://github.com/browserify/to-buffer/compare/v1.0.1...v1.1.0) - 2017-04-12

### Merged

- Fix typo [`#2`](https://github.com/browserify/to-buffer/pull/2)

### Commits

- support arrays as well [`ef98c82`](https://github.com/browserify/to-buffer/commit/ef98c82791d71601077577e84c4614ec2d05f086)

## [v1.0.1](https://github.com/browserify/to-buffer/compare/v1.0.0...v1.0.1) - 2016-02-15

### Commits

- fix desc [`7d50a7c`](https://github.com/browserify/to-buffer/commit/7d50a7c69c3eef77448893744ada16601c44af6a)

## v1.0.0 - 2016-02-15

### Commits

- first commit [`8361941`](https://github.com/browserify/to-buffer/commit/8361941d7acb3b82c732ecd10bdb047da5af2028)
- add travis [`de911b5`](https://github.com/browserify/to-buffer/commit/de911b5364558561d84b4ec9e43c6a0fe1c8e904)
