const redis = require('redis');
const logger = require('../utils/logger');

let redisClient = null;

// Redis 配置
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT) || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: parseInt(process.env.REDIS_DB) || 0,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
};

// 連接 Redis
async function connectRedis() {
  try {
    redisClient = redis.createClient({
      socket: {
        host: redisConfig.host,
        port: redisConfig.port,
      },
      password: redisConfig.password,
      database: redisConfig.db,
    });

    // 錯誤處理
    redisClient.on('error', (error) => {
      logger.error('Redis 連接錯誤:', error);
    });

    redisClient.on('connect', () => {
      logger.info('Redis 連接建立');
    });

    redisClient.on('ready', () => {
      logger.info('Redis 準備就緒');
    });

    redisClient.on('end', () => {
      logger.info('Redis 連接結束');
    });

    // 連接到 Redis
    await redisClient.connect();
    
    return redisClient;
  } catch (error) {
    logger.error('Redis 連接失敗:', error);
    throw error;
  }
}

// 獲取 Redis 客戶端
function getRedisClient() {
  if (!redisClient) {
    throw new Error('Redis 客戶端未初始化');
  }
  return redisClient;
}

// 關閉 Redis 連接
async function closeRedis() {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
    logger.info('Redis 連接已關閉');
  }
}

module.exports = {
  connectRedis,
  getRedisClient,
  closeRedis,
};
