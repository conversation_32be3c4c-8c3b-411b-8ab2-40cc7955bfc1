{"name": "pbkdf2", "version": "3.1.3", "description": "This library provides the functionality of PBKDF2 with the ability to use any supported hashing algorithm returned from crypto.getHashes()", "keywords": ["pbkdf2", "kdf", "salt", "hash"], "homepage": "https://github.com/crypto-browserify/pbkdf2", "bugs": {"url": "https://github.com/crypto-browserify/pbkdf2/issues"}, "license": "MIT", "author": "<PERSON>", "browser": {"./index.js": "./browser.js", "./lib/sync.js": "./lib/sync-browser.js"}, "main": "index.js", "repository": {"type": "git", "url": "https://github.com/crypto-browserify/pbkdf2.git"}, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "lint": "eslint --ext=js,mjs .", "pretest": "npm run lint", "tests-only": "nyc tape test/index.js", "test": "npm run tests-only && npm run bundle-test", "posttest": "npx npm@\">= 10.2\" audit --production", "bundle-test": "browserify test/index.js > test/bundle.js", "bench": "node bench/", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "auto-changelog": "^2.5.0", "benchmark": "^2.1.4", "browserify": "^17.0.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object.assign": "^4.1.7", "semver": "^6.3.1", "tape": "^5.9.0"}, "dependencies": {"create-hash": "~1.1.3", "create-hmac": "^1.1.7", "ripemd160": "=2.0.1", "safe-buffer": "^5.2.1", "sha.js": "^2.4.11", "to-buffer": "^1.2.0"}, "engines": {"node": ">=0.12"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows", "bench", "test"]}}