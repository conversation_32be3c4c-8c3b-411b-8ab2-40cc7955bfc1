{"name": "react-native-quick-base64", "version": "2.2.1", "description": "A native implementation of base64 in C++ for React Native", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"source": "./src/index.ts", "types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "android", "ios", "cpp", "*.podsp<PERSON>", "react-native.config.js", "!ios/build", "!android/build", "!android/gradle", "!android/gradlew", "!android/gradlew.bat", "!android/local.properties", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__", "!**/.*"], "scripts": {"example": "yarn workspace react-native-quick-base64-example", "test": "jest", "typecheck": "tsc", "lint": "eslint \"**/*.{js,ts,tsx}\"", "clean": "del-cli android/build example/android/build example/android/app/build example/ios/build lib", "prepare": "bob build", "release": "release-it --only-version"}, "keywords": ["react-native", "ios", "android"], "repository": {"type": "git", "url": "git+https://github.com/craftzdog/react-native-quick-base64.git"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/craftzdog)", "license": "MIT", "bugs": {"url": "https://github.com/craftzdog/react-native-quick-base64/issues"}, "homepage": "https://github.com/craftzdog/react-native-quick-base64#readme", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@commitlint/config-conventional": "^19.6.0", "@eslint/compat": "^1.2.7", "@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.22.0", "@evilmartians/lefthook": "^1.5.0", "@react-native-community/cli": "15.0.0-alpha.2", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "^0.78.0", "@release-it/conventional-changelog": "^9.0.2", "@types/jest": "^29.5.5", "@types/react": "^19.0.0", "commitlint": "^19.6.1", "del-cli": "^5.1.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "jest": "^29.7.0", "prettier": "^3.0.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-builder-bob": "^0.40.11", "release-it": "^17.10.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": "*", "react-native": "*"}, "workspaces": ["example"], "packageManager": "yarn@4.9.1", "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": {"name": "angular"}}}}, "prettier": {"arrowParens": "avoid", "singleQuote": true, "bracketSpacing": true, "endOfLine": "lf", "semi": false, "tabWidth": 2, "trailingComma": "none"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "create-react-native-library": {"languages": "kotlin-objc", "type": "turbo-module", "version": "0.50.3"}}