// 簡單的 API 測試腳本
const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

async function testRegister() {
  try {
    console.log('測試用戶註冊...');
    const response = await axios.post(`${BASE_URL}/auth/register`, {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    });
    
    console.log('註冊成功:', response.data);
    return response.data.data.token;
  } catch (error) {
    console.error('註冊失敗:', error.response?.data || error.message);
    return null;
  }
}

async function testLogin() {
  try {
    console.log('\n測試用戶登入...');
    const response = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });
    
    console.log('登入成功:', response.data);
    return response.data.data.token;
  } catch (error) {
    console.error('登入失敗:', error.response?.data || error.message);
    return null;
  }
}

async function testGetCurrentUser(token) {
  try {
    console.log('\n測試獲取當前用戶...');
    const response = await axios.get(`${BASE_URL}/auth/me`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    
    console.log('獲取用戶資訊成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('獲取用戶資訊失敗:', error.response?.data || error.message);
    return null;
  }
}

async function testSolanaAPI() {
  try {
    console.log('\n測試 Solana API...');
    const response = await axios.get(`${BASE_URL}/solana/connection-check`);
    console.log('Solana 連接檢查:', response.data);
  } catch (error) {
    console.error('Solana API 測試失敗:', error.response?.data || error.message);
  }
}

async function runTests() {
  console.log('開始 API 測試...\n');
  
  // 測試 Solana API
  await testSolanaAPI();
  
  // 測試註冊
  const registerToken = await testRegister();
  
  // 測試登入
  const loginToken = await testLogin();
  
  // 測試獲取用戶資訊
  if (loginToken) {
    await testGetCurrentUser(loginToken);
  }
  
  console.log('\nAPI 測試完成！');
}

// 運行測試
runTests().catch(console.error);
