const express = require('express');
const router = express.Router();
const { solanaService } = require('../services/solanaService');
const logger = require('../utils/logger');

// 獲取網路狀態
router.get('/network-status', async (req, res, next) => {
  try {
    const status = await solanaService.getNetworkStatus();
    res.json({
      success: true,
      message: '獲取網路狀態成功',
      data: status,
    });
  } catch (error) {
    next(error);
  }
});

// 檢查連接狀態
router.get('/connection-check', async (req, res, next) => {
  try {
    const isConnected = await solanaService.checkConnection();
    res.json({
      success: true,
      message: isConnected ? 'Solana 連接正常' : 'Solana 連接異常',
      data: { connected: isConnected },
    });
  } catch (error) {
    next(error);
  }
});

// 獲取帳戶餘額
router.get('/balance/:publicKey', async (req, res, next) => {
  try {
    const { publicKey } = req.params;
    
    if (!publicKey) {
      return res.status(400).json({
        success: false,
        message: '缺少公鑰參數',
      });
    }

    const balance = await solanaService.getBalance(publicKey);
    res.json({
      success: true,
      message: '獲取餘額成功',
      data: {
        publicKey,
        balance,
        unit: 'SOL',
      },
    });
  } catch (error) {
    next(error);
  }
});

// 獲取帳戶資訊
router.get('/account/:publicKey', async (req, res, next) => {
  try {
    const { publicKey } = req.params;
    
    if (!publicKey) {
      return res.status(400).json({
        success: false,
        message: '缺少公鑰參數',
      });
    }

    const accountInfo = await solanaService.getAccountInfo(publicKey);
    res.json({
      success: true,
      message: accountInfo ? '獲取帳戶資訊成功' : '帳戶不存在',
      data: {
        publicKey,
        accountInfo,
      },
    });
  } catch (error) {
    next(error);
  }
});

// 獲取交易歷史
router.get('/transactions/:publicKey', async (req, res, next) => {
  try {
    const { publicKey } = req.params;
    const { limit = 10 } = req.query;
    
    if (!publicKey) {
      return res.status(400).json({
        success: false,
        message: '缺少公鑰參數',
      });
    }

    const transactions = await solanaService.getTransactionHistory(
      publicKey,
      parseInt(limit)
    );
    
    res.json({
      success: true,
      message: '獲取交易歷史成功',
      data: {
        publicKey,
        transactions,
        count: transactions.length,
      },
    });
  } catch (error) {
    next(error);
  }
});

// 驗證簽名
router.post('/verify-signature', async (req, res, next) => {
  try {
    const { message, signature, publicKey } = req.body;
    
    if (!message || !signature || !publicKey) {
      return res.status(400).json({
        success: false,
        message: '缺少必要參數: message, signature, publicKey',
      });
    }

    const isValid = await solanaService.verifySignature(message, signature, publicKey);
    
    res.json({
      success: true,
      message: isValid ? '簽名驗證成功' : '簽名驗證失敗',
      data: {
        valid: isValid,
        publicKey,
      },
    });
  } catch (error) {
    next(error);
  }
});

// 驗證公鑰格式
router.post('/validate-publickey', async (req, res, next) => {
  try {
    const { publicKey } = req.body;
    
    if (!publicKey) {
      return res.status(400).json({
        success: false,
        message: '缺少公鑰參數',
      });
    }

    const isValid = solanaService.isValidPublicKey(publicKey);
    
    res.json({
      success: true,
      message: isValid ? '公鑰格式有效' : '公鑰格式無效',
      data: {
        publicKey,
        valid: isValid,
      },
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
