# ChainXiang 象棋App 完整實作計畫

## 1. 技術架構概覽

### 1.1 整體架構圖

```mermaid
graph TB
    subgraph "Mobile App (React Native)"
        A1[登入模組]
        A2[遊戲介面]
        A3[對戰模組]
        A4[錢包整合]
        A5[狀態管理]
    end
    
    subgraph "Backend Server (Node.js)"
        B1[API Gateway]
        B2[認證服務]
        B3[遊戲邏輯]
        B4[匹配系統]
        B5[WebSocket服務]
    end
    
    subgraph "Database (MongoDB)"
        C1[用戶資料]
        C2[遊戲記錄]
        C3[匹配隊列]
    end
    
    subgraph "Solana Blockchain"
        D1[錢包認證]
        D2[對戰記錄]
        D3[NFT棋子]
        D4[代幣獎勵]
    end
    
    A1 --> B1
    A2 --> B5
    A3 --> B3
    A4 --> D1
    B1 --> B2
    B2 --> C1
    B3 --> C2
    B4 --> C3
    B5 --> B3
    B2 --> D1
    B3 --> D2
```

### 1.2 技術棧詳細規格

| 層級 | 技術 | 版本 | 用途 |
|------|------|------|------|
| **前端框架** | React Native | 0.72+ | 跨平台移動應用開發 |
| **狀態管理** | Redux Toolkit | 1.9+ | 應用狀態管理 |
| **UI組件** | React Native Elements | 3.4+ | UI組件庫 |
| **動畫** | React Native Reanimated | 3.0+ | 棋子移動動畫 |
| **WebSocket** | Socket.IO Client | 4.7+ | 即時通訊 |
| **Solana整合** | @solana/wallet-adapter-react-native | 2.0+ | 錢包連接 |
| **後端框架** | Node.js + Express | 18+ / 4.18+ | 後端API服務 |
| **即時通訊** | Socket.IO | 4.7+ | WebSocket服務 |
| **資料庫** | MongoDB + Mongoose | 6.0+ / 7.0+ | 數據存儲 |
| **區塊鏈** | @solana/web3.js | 1.78+ | Solana區塊鏈互動 |

## 2. 系統流程圖

### 2.1 用戶登入流程

```mermaid
sequenceDiagram
    participant U as 用戶
    participant A as App
    participant W as Solana錢包
    participant B as 後端
    participant S as Solana區塊鏈
    
    U->>A: 點擊登入
    A->>W: 請求連接錢包
    W->>U: 確認連接
    U->>W: 授權連接
    W->>A: 返回錢包地址
    A->>W: 請求簽名訊息
    W->>U: 確認簽名
    U->>W: 簽名確認
    W->>A: 返回簽名
    A->>B: 發送地址+簽名
    B->>S: 驗證簽名
    S->>B: 驗證結果
    B->>A: 返回JWT Token
    A->>U: 登入成功
```

### 2.2 PVP對戰流程

```mermaid
sequenceDiagram
    participant P1 as 玩家1
    participant P2 as 玩家2
    participant B as 後端
    participant DB as 資料庫
    
    P1->>B: 請求匹配
    P2->>B: 請求匹配
    B->>B: 匹配算法
    B->>DB: 創建遊戲房間
    B->>P1: 匹配成功
    B->>P2: 匹配成功
    
    loop 遊戲進行中
        P1->>B: 發送走棋
        B->>B: 驗證棋步
        B->>DB: 更新遊戲狀態
        B->>P2: 廣播棋步
        P2->>B: 發送走棋
        B->>B: 驗證棋步
        B->>DB: 更新遊戲狀態
        B->>P1: 廣播棋步
    end
    
    B->>B: 判斷勝負
    B->>DB: 記錄遊戲結果
    B->>P1: 遊戲結束
    B->>P2: 遊戲結束
```

## 3. 專案目錄結構

```
ChainXiang/
├── docs/                           # 文檔目錄
│   ├── 完整實作計畫.md
│   ├── 象棋App開發規劃.md
│   ├── chess_design_plan.md
│   ├── API文檔.md
│   └── 部署指南.md
├── mobile/                         # React Native 移動應用
│   ├── src/
│   │   ├── components/             # 可重用組件
│   │   │   ├── common/             # 通用組件
│   │   │   ├── chess/              # 象棋相關組件
│   │   │   │   ├── ChessBoard.js   # 棋盤組件
│   │   │   │   ├── ChessPiece.js   # 棋子組件
│   │   │   │   └── GameTimer.js    # 計時器組件
│   │   │   └── ui/                 # UI組件
│   │   ├── screens/                # 頁面組件
│   │   │   ├── AuthScreen.js       # 登入頁面
│   │   │   ├── HomeScreen.js       # 主頁
│   │   │   ├── GameScreen.js       # 遊戲頁面
│   │   │   ├── ProfileScreen.js    # 個人資料
│   │   │   └── HistoryScreen.js    # 歷史記錄
│   │   ├── services/               # 服務層
│   │   │   ├── api.js              # API調用
│   │   │   ├── websocket.js        # WebSocket服務
│   │   │   ├── solana.js           # Solana整合
│   │   │   └── storage.js          # 本地存儲
│   │   ├── store/                  # Redux狀態管理
│   │   │   ├── slices/             # Redux切片
│   │   │   │   ├── authSlice.js    # 認證狀態
│   │   │   │   ├── gameSlice.js    # 遊戲狀態
│   │   │   │   └── userSlice.js    # 用戶狀態
│   │   │   └── store.js            # Store配置
│   │   ├── utils/                  # 工具函數
│   │   │   ├── chessLogic.js       # 象棋邏輯
│   │   │   ├── constants.js        # 常量定義
│   │   │   └── helpers.js          # 輔助函數
│   │   ├── assets/                 # 靜態資源
│   │   │   ├── images/             # 圖片資源
│   │   │   │   ├── pieces/         # 棋子圖片
│   │   │   │   ├── boards/         # 棋盤圖片
│   │   │   │   └── ui/             # UI圖片
│   │   │   ├── sounds/             # 音效資源
│   │   │   └── fonts/              # 字體資源
│   │   └── App.js                  # 應用入口
│   ├── android/                    # Android配置
│   ├── ios/                        # iOS配置
│   ├── package.json
│   └── metro.config.js
├── backend/                        # Node.js 後端服務
│   ├── src/
│   │   ├── controllers/            # 控制器
│   │   │   ├── authController.js   # 認證控制器
│   │   │   ├── gameController.js   # 遊戲控制器
│   │   │   └── userController.js   # 用戶控制器
│   │   ├── middleware/             # 中間件
│   │   │   ├── auth.js             # 認證中間件
│   │   │   ├── validation.js       # 驗證中間件
│   │   │   └── errorHandler.js     # 錯誤處理
│   │   ├── models/                 # 數據模型
│   │   │   ├── User.js             # 用戶模型
│   │   │   ├── Game.js             # 遊戲模型
│   │   │   └── GameRoom.js         # 遊戲房間模型
│   │   ├── routes/                 # 路由定義
│   │   │   ├── auth.js             # 認證路由
│   │   │   ├── game.js             # 遊戲路由
│   │   │   └── user.js             # 用戶路由
│   │   ├── services/               # 業務邏輯服務
│   │   │   ├── authService.js      # 認證服務
│   │   │   ├── gameService.js      # 遊戲服務
│   │   │   ├── matchService.js     # 匹配服務
│   │   │   └── solanaService.js    # Solana服務
│   │   ├── socket/                 # WebSocket處理
│   │   │   ├── gameSocket.js       # 遊戲Socket
│   │   │   └── matchSocket.js      # 匹配Socket
│   │   ├── utils/                  # 工具函數
│   │   │   ├── chessEngine.js      # 象棋引擎
│   │   │   ├── validation.js       # 驗證工具
│   │   │   └── constants.js        # 常量
│   │   ├── config/                 # 配置文件
│   │   │   ├── database.js         # 資料庫配置
│   │   │   ├── solana.js           # Solana配置
│   │   │   └── environment.js      # 環境配置
│   │   └── app.js                  # 應用入口
│   ├── package.json
│   └── .env.example
├── smart-contracts/                # Solana智能合約 (可選)
│   ├── programs/
│   │   └── chess-game/
│   │       ├── src/
│   │       │   ├── lib.rs          # 合約主邏輯
│   │       │   ├── state.rs        # 狀態定義
│   │       │   └── instructions.rs # 指令處理
│   │       └── Cargo.toml
│   ├── tests/
│   └── Anchor.toml
├── resources/                      # 資源文件目錄
│   ├── images/                     # 圖片資源
│   ├── sounds/                     # 音效資源
│   ├── fonts/                      # 字體資源
│   └── resource-list.md            # 資源清單
├── scripts/                        # 腳本文件
│   ├── setup.sh                    # 環境設置腳本
│   ├── build.sh                    # 構建腳本
│   └── deploy.sh                   # 部署腳本
├── tests/                          # 測試文件
│   ├── unit/                       # 單元測試
│   ├── integration/                # 集成測試
│   └── e2e/                        # 端到端測試
├── .gitignore
├── README.md
└── package.json                    # 根目錄package.json
```

## 4. 核心模組設計

### 4.1 前端核心模組

#### 4.1.1 認證模組 (AuthModule)
- **功能**: Solana錢包連接、用戶登入/登出
- **主要組件**:
  - `WalletConnector`: 錢包連接組件
  - `AuthScreen`: 登入頁面
  - `authSlice`: 認證狀態管理
- **關鍵API**:
  - `connectWallet()`: 連接錢包
  - `signMessage()`: 簽名訊息
  - `login()`: 用戶登入
  - `logout()`: 用戶登出

#### 4.1.2 遊戲模組 (GameModule)
- **功能**: 象棋遊戲邏輯、UI渲染、用戶交互
- **主要組件**:
  - `ChessBoard`: 棋盤組件
  - `ChessPiece`: 棋子組件
  - `GameScreen`: 遊戲主頁面
  - `GameTimer`: 計時器組件
- **關鍵API**:
  - `movePiece()`: 移動棋子
  - `validateMove()`: 驗證走棋
  - `updateGameState()`: 更新遊戲狀態
  - `checkGameEnd()`: 檢查遊戲結束

#### 4.1.3 對戰模組 (PVPModule)
- **功能**: 玩家匹配、即時對戰、狀態同步
- **主要組件**:
  - `MatchMaking`: 匹配組件
  - `GameRoom`: 遊戲房間
  - `websocketService`: WebSocket服務
- **關鍵API**:
  - `findMatch()`: 尋找對手
  - `joinRoom()`: 加入房間
  - `sendMove()`: 發送走棋
  - `receiveMove()`: 接收走棋

### 4.2 後端核心模組

#### 4.2.1 認證服務 (AuthService)
- **功能**: Solana簽名驗證、JWT管理
- **主要功能**:
  - 驗證Solana錢包簽名
  - 生成和驗證JWT Token
  - 用戶會話管理
- **關鍵API**:
  - `verifySignature()`: 驗證簽名
  - `generateToken()`: 生成Token
  - `validateToken()`: 驗證Token

#### 4.2.2 遊戲引擎 (ChessEngine)
- **功能**: 象棋規則實現、走棋驗證、勝負判斷
- **主要功能**:
  - 實現所有棋子移動規則
  - 驗證走棋合法性
  - 判斷將軍、將死、和棋等狀態
- **關鍵API**:
  - `isValidMove()`: 驗證走棋
  - `makeMove()`: 執行走棋
  - `checkGameStatus()`: 檢查遊戲狀態
  - `generateMoves()`: 生成可能走棋

#### 4.2.3 匹配系統 (MatchingSystem)
- **功能**: 玩家匹配、房間管理、負載均衡
- **主要功能**:
  - 基於等級的匹配算法
  - 遊戲房間創建和管理
  - 玩家隊列管理
- **關鍵API**:
  - `addToQueue()`: 加入匹配隊列
  - `findOpponent()`: 尋找對手
  - `createRoom()`: 創建房間
  - `removeFromQueue()`: 移出隊列

## 5. 資料庫設計

### 5.1 用戶資料模型 (User Schema)
```javascript
{
  _id: ObjectId,
  walletAddress: String,      // Solana錢包地址
  username: String,           // 用戶暱稱
  avatar: String,             // 頭像URL
  level: Number,              // 用戶等級
  rating: Number,             // 積分等級
  stats: {
    totalGames: Number,       // 總遊戲數
    wins: Number,             // 勝場
    losses: Number,           // 敗場
    draws: Number             // 和局
  },
  createdAt: Date,
  updatedAt: Date
}
```

### 5.2 遊戲記錄模型 (Game Schema)
```javascript
{
  _id: ObjectId,
  gameId: String,             // 遊戲唯一ID
  players: {
    red: ObjectId,            // 紅方玩家ID
    black: ObjectId           // 黑方玩家ID
  },
  gameState: {
    board: Array,             // 棋盤狀態
    currentPlayer: String,    // 當前玩家
    moveHistory: Array,       // 走棋歷史
    status: String            // 遊戲狀態
  },
  result: {
    winner: String,           // 勝利方
    reason: String,           // 結束原因
    endTime: Date             // 結束時間
  },
  startTime: Date,
  endTime: Date,
  duration: Number            // 遊戲時長
}
```

### 5.3 遊戲房間模型 (GameRoom Schema)
```javascript
{
  _id: ObjectId,
  roomId: String,             // 房間唯一ID
  players: [{
    userId: ObjectId,
    socketId: String,
    color: String,            // 'red' or 'black'
    isReady: Boolean,
    lastActivity: Date
  }],
  gameState: {
    board: Array,
    currentPlayer: String,
    moveCount: Number,
    timer: {
      red: Number,            // 紅方剩餘時間
      black: Number           // 黑方剩餘時間
    }
  },
  status: String,             // 'waiting', 'playing', 'finished'
  createdAt: Date,
  updatedAt: Date
}
```

## 6. API設計規範

### 6.1 RESTful API端點

#### 認證相關
- `POST /api/auth/login` - 用戶登入
- `POST /api/auth/logout` - 用戶登出
- `GET /api/auth/profile` - 獲取用戶資料
- `PUT /api/auth/profile` - 更新用戶資料

#### 遊戲相關
- `GET /api/games/history` - 獲取遊戲歷史
- `GET /api/games/:gameId` - 獲取特定遊戲詳情
- `POST /api/games/replay` - 獲取遊戲回放

#### 匹配相關
- `POST /api/match/queue` - 加入匹配隊列
- `DELETE /api/match/queue` - 離開匹配隊列
- `GET /api/match/status` - 獲取匹配狀態

### 6.2 WebSocket事件

#### 匹配事件
- `match_request` - 請求匹配
- `match_found` - 找到對手
- `match_cancelled` - 匹配取消

#### 遊戲事件
- `game_start` - 遊戲開始
- `move_made` - 走棋
- `move_invalid` - 無效走棋
- `game_end` - 遊戲結束
- `player_disconnect` - 玩家斷線
- `player_reconnect` - 玩家重連

## 7. Solana區塊鏈整合

### 7.1 錢包整合
- **支援錢包**: Phantom, Solflare, Backpack
- **連接方式**: Solana Mobile Wallet Adapter
- **功能**:
  - 錢包連接和授權
  - 訊息簽名驗證
  - 交易簽名 (未來功能)

### 7.2 鏈上數據結構 (未來擴展)
```rust
// 遊戲結果記錄
pub struct GameResult {
    pub game_id: String,
    pub players: [Pubkey; 2],
    pub winner: Option<Pubkey>,
    pub moves_count: u16,
    pub timestamp: i64,
}

// NFT棋子元數據
pub struct ChessPieceNFT {
    pub piece_type: PieceType,
    pub rarity: Rarity,
    pub owner: Pubkey,
    pub metadata_uri: String,
}
```

## 8. 開發流程規劃

### 8.1 開發階段劃分

#### 第一階段：基礎架構搭建 (1-2週)
- [ ] 專案初始化和環境設置
- [ ] React Native專案創建
- [ ] Node.js後端專案創建
- [ ] MongoDB資料庫設置
- [ ] 基礎目錄結構建立
- [ ] 開發環境配置

#### 第二階段：核心功能開發 (3-4週)
- [ ] Solana錢包整合
- [ ] 用戶認證系統
- [ ] 象棋遊戲邏輯實現
- [ ] 基礎UI組件開發
- [ ] WebSocket即時通訊
- [ ] 資料庫模型設計

#### 第三階段：遊戲功能完善 (2-3週)
- [ ] PVP匹配系統
- [ ] 遊戲房間管理
- [ ] 棋盤和棋子UI
- [ ] 遊戲狀態同步
- [ ] 計時器功能
- [ ] 勝負判斷邏輯

#### 第四階段：用戶體驗優化 (1-2週)
- [ ] UI/UX設計實現
- [ ] 動畫效果添加
- [ ] 音效整合
- [ ] 錯誤處理優化
- [ ] 性能優化
- [ ] 斷線重連機制

#### 第五階段：測試和部署 (1-2週)
- [ ] 單元測試編寫
- [ ] 集成測試
- [ ] 端到端測試
- [ ] 性能測試
- [ ] 安全性測試
- [ ] 部署配置

### 8.2 技術里程碑

#### 里程碑1：基礎架構完成
- React Native專案可以運行
- 後端API服務啟動
- 資料庫連接成功
- 基礎路由配置完成

#### 里程碑2：認證系統完成
- Solana錢包連接功能
- 用戶登入/登出流程
- JWT Token管理
- 用戶資料CRUD操作

#### 里程碑3：遊戲核心完成
- 象棋規則引擎
- 棋盤UI渲染
- 棋子移動邏輯
- 基礎遊戲流程

#### 里程碑4：PVP功能完成
- 玩家匹配系統
- 即時對戰功能
- 遊戲狀態同步
- 勝負判斷

#### 里程碑5：產品就緒
- 完整功能測試通過
- 性能指標達標
- 部署環境就緒
- 文檔完善

### 8.3 開發工具和環境

#### 開發環境
- **IDE**: Visual Studio Code
- **Node.js**: v18.x LTS
- **React Native CLI**: 最新版本
- **Android Studio**: 最新版本 (Android開發)
- **Xcode**: 最新版本 (iOS開發)

#### 版本控制
- **Git**: 版本控制
- **GitHub**: 代碼託管
- **分支策略**: GitFlow

#### 測試工具
- **Jest**: JavaScript單元測試
- **Detox**: React Native端到端測試
- **Supertest**: API測試
- **MongoDB Memory Server**: 資料庫測試

#### 部署工具
- **Docker**: 容器化部署
- **PM2**: Node.js進程管理
- **Nginx**: 反向代理
- **Let's Encrypt**: SSL證書

## 9. 部署架構

### 9.1 生產環境架構

```mermaid
graph TB
    subgraph "CDN"
        CDN[CloudFlare CDN]
    end

    subgraph "Load Balancer"
        LB[Nginx Load Balancer]
    end

    subgraph "Application Servers"
        APP1[Node.js Server 1]
        APP2[Node.js Server 2]
    end

    subgraph "Database Cluster"
        DB1[MongoDB Primary]
        DB2[MongoDB Secondary]
        DB3[MongoDB Arbiter]
    end

    subgraph "Cache Layer"
        REDIS[Redis Cluster]
    end

    subgraph "Monitoring"
        MON[Monitoring Stack]
    end

    CDN --> LB
    LB --> APP1
    LB --> APP2
    APP1 --> DB1
    APP2 --> DB1
    DB1 --> DB2
    DB1 --> DB3
    APP1 --> REDIS
    APP2 --> REDIS
    MON --> APP1
    MON --> APP2
    MON --> DB1
```

### 9.2 部署配置

#### Docker配置
```dockerfile
# backend/Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
```

#### Nginx配置
```nginx
upstream backend {
    server app1:3000;
    server app2:3000;
}

server {
    listen 80;
    server_name api.chainxiang.com;

    location / {
        proxy_pass http://backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 9.3 監控和日誌

#### 監控指標
- **系統指標**: CPU、記憶體、磁碟、網路
- **應用指標**: 響應時間、錯誤率、吞吐量
- **業務指標**: 用戶數、遊戲數、匹配成功率

#### 日誌管理
- **應用日誌**: Winston + ELK Stack
- **訪問日誌**: Nginx日誌
- **錯誤追蹤**: Sentry

## 10. 安全性考量

### 10.1 前端安全
- **代碼混淆**: 保護JavaScript代碼
- **API加密**: HTTPS通訊
- **輸入驗證**: 防止XSS攻擊
- **敏感資料**: 不在前端存儲私鑰

### 10.2 後端安全
- **認證授權**: JWT Token + 簽名驗證
- **輸入驗證**: 防止SQL注入和NoSQL注入
- **速率限制**: 防止DDoS攻擊
- **CORS配置**: 跨域請求控制

### 10.3 區塊鏈安全
- **簽名驗證**: 嚴格驗證Solana簽名
- **私鑰管理**: 使用錢包管理，不直接處理
- **交易驗證**: 多重驗證機制
- **智能合約**: 代碼審計和測試

## 11. 性能優化

### 11.1 前端優化
- **代碼分割**: 按需載入組件
- **圖片優化**: WebP格式和懶載入
- **狀態管理**: 避免不必要的重渲染
- **記憶體管理**: 及時清理事件監聽器

### 11.2 後端優化
- **資料庫索引**: 優化查詢性能
- **連接池**: 資料庫連接管理
- **快取策略**: Redis快取熱點資料
- **負載均衡**: 水平擴展

### 11.3 網路優化
- **CDN**: 靜態資源分發
- **壓縮**: Gzip壓縮
- **Keep-Alive**: 持久連接
- **WebSocket**: 減少HTTP請求

## 12. 實作檢查清單

### 12.1 開發前準備 ✅ **已完成**
- [x] 確認所有技術規格和需求
- [x] 設置完整的開發環境
- [x] 準備基礎資源文件結構
- [x] 建立專案倉庫和分支策略
- [x] 配置基礎開發工具 (ESLint, Git hooks)

### 12.2 核心功能實作 🔄 **進行中**
- [x] 象棋遊戲邏輯和規則引擎 ✅ **完成**
- [x] React Native UI組件和頁面 (基礎) ✅ **完成**
- [x] 資料庫設計和架構 ✅ **完成**
- [ ] Solana錢包整合和認證系統 🔄 **當前重點**
- [ ] WebSocket即時通訊系統
- [ ] PVP匹配和對戰功能
- [ ] 後端API接口實現

### 12.3 品質保證
- [ ] 單元測試覆蓋率 > 80%
- [ ] 集成測試和端到端測試
- [ ] 性能測試和優化
- [ ] 安全性測試和漏洞掃描
- [ ] 多設備兼容性測試

### 12.4 部署準備
- [ ] 生產環境配置
- [ ] Docker容器化
- [ ] 監控和日誌系統
- [ ] 備份和災難恢復計畫
- [ ] 應用商店發布準備

## 13. 相關文檔

本實作計畫包含以下相關文檔：

1. **[完整實作計畫.md](./完整實作計畫.md)** - 本文檔，包含完整的技術架構和實作規劃
2. **[開發時程表.md](./開發時程表.md)** - 詳細的10-12週開發時程安排
3. **[技術規格文檔.md](./技術規格文檔.md)** - 詳細的技術規格和API設計
4. **[../resources/resource-list.md](../resources/resource-list.md)** - 完整的資源清單和路徑規劃
5. **[象棋App開發規劃.md](./象棋App開發規劃.md)** - 原始需求分析和技術選型
6. **[chess_design_plan.md](./chess_design_plan.md)** - 棋子設計規劃

## 14. 下一步行動

### 立即行動項目
1. **確認實作計畫** - 請仔細審查所有文檔，確認技術選型和架構設計
2. **準備資源文件** - 根據資源清單準備所需的圖片、音效等資源
3. **環境設置** - 安裝所有必要的開發工具和依賴
4. **專案初始化** - 創建專案倉庫和基礎架構

### 確認事項
- [ ] 技術架構是否符合需求？
- [ ] 開發時程是否合理？
- [ ] 資源清單是否完整？
- [ ] 是否有遺漏的重要功能？
- [ ] 團隊技能是否匹配技術選型？

### 風險評估
- **高風險**: Solana整合複雜度、WebSocket穩定性
- **中風險**: 跨平台兼容性、性能優化
- **低風險**: UI實作、基礎功能開發

請確認以上所有規劃內容，如有任何需要調整的地方，請告知。確認無誤後即可開始實作開發。
```
