const mongoose = require('mongoose');
const logger = require('../utils/logger');

const connectDatabase = async() => {
  try {
    const mongoUri = process.env.NODE_ENV === 'test'
      ? process.env.MONGODB_TEST_URI
      : process.env.MONGODB_URI;

    if (!mongoUri) {
      throw new Error('MongoDB URI 未設置');
    }

    const options = {
      maxPoolSize: 10, // 最大連接池大小
      serverSelectionTimeoutMS: 5000, // 服務器選擇超時
      socketTimeoutMS: 45000, // Socket 超時
      bufferCommands: false, // 禁用 mongoose 緩衝
    };

    await mongoose.connect(mongoUri, options);

    // 連接事件監聽
    mongoose.connection.on('connected', () => {
      logger.info('MongoDB 連接成功');
    });

    mongoose.connection.on('error', (error) => {
      logger.error('MongoDB 連接錯誤:', error);
    });

    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB 連接斷開');
    });

    // 應用終止時關閉連接
    process.on('SIGINT', async() => {
      await mongoose.connection.close();
      logger.info('MongoDB 連接已關閉');
      process.exit(0);
    });

  } catch (error) {
    logger.error('MongoDB 連接失敗:', error);
    throw error;
  }
};

module.exports = {
  connectDatabase,
};
