const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const morgan = require('morgan');
require('dotenv').config();

// 導入配置和中間件
const { connectDatabase } = require('./config/database');
const { connectRedis } = require('./config/redis');
const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');

// 導入路由
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/user');
const gameRoutes = require('./routes/game');

// 導入 Socket 處理器
const gameSocket = require('./socket/gameSocket');
const matchSocket = require('./socket/matchSocket');

const app = express();
const server = http.createServer(app);

// Socket.IO 配置
const io = socketIo(server, {
  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
    methods: ['GET', 'POST'],
    credentials: true,
  },
  transports: ['websocket', 'polling'],
});

// 基礎中間件
app.use(helmet());
app.use(compression());
app.use(cors({
  origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'],
  credentials: true,
}));

// 速率限制
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分鐘
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // 限制每個IP 100個請求
  message: {
    error: '請求過於頻繁，請稍後再試',
  },
});
app.use('/api/', limiter);

// 日誌中間件
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim()),
  },
}));

// 解析中間件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 健康檢查端點
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  });
});

// API 路由
app.use('/api/auth', authRoutes);
app.use('/api/user', userRoutes);
app.use('/api/game', gameRoutes);

// 404 處理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '找不到請求的資源',
  });
});

// 錯誤處理中間件
app.use(errorHandler);

// Socket.IO 連接處理
io.on('connection', (socket) => {
  logger.info(`用戶連接: ${socket.id}`);

  // 註冊 Socket 事件處理器
  gameSocket(io, socket);
  matchSocket(io, socket);

  socket.on('disconnect', (reason) => {
    logger.info(`用戶斷開連接: ${socket.id}, 原因: ${reason}`);
  });

  socket.on('error', (error) => {
    logger.error(`Socket 錯誤: ${socket.id}`, error);
  });
});

// 啟動服務器
const PORT = process.env.PORT || 3000;

async function startServer() {
  try {
    // 連接資料庫
    await connectDatabase();
    logger.info('MongoDB 連接成功');

    // 連接 Redis
    await connectRedis();
    logger.info('Redis 連接成功');

    // 啟動服務器
    server.listen(PORT, () => {
      logger.info(`服務器運行在端口 ${PORT}`);
      logger.info(`環境: ${process.env.NODE_ENV}`);
    });

    // 優雅關閉處理
    process.on('SIGTERM', gracefulShutdown);
    process.on('SIGINT', gracefulShutdown);

  } catch (error) {
    logger.error('服務器啟動失敗:', error);
    process.exit(1);
  }
}

// 優雅關閉
function gracefulShutdown(signal) {
  logger.info(`收到 ${signal} 信號，開始優雅關閉...`);

  server.close(() => {
    logger.info('HTTP 服務器已關閉');

    // 關閉資料庫連接
    const mongoose = require('mongoose');
    mongoose.connection.close(() => {
      logger.info('MongoDB 連接已關閉');
      process.exit(0);
    });
  });

  // 強制關閉超時
  setTimeout(() => {
    logger.error('強制關閉服務器');
    process.exit(1);
  }, 10000);
}

// 未捕獲的異常處理
process.on('uncaughtException', (error) => {
  logger.error('未捕獲的異常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  logger.error('未處理的 Promise 拒絕:', reason);
  process.exit(1);
});

// 啟動服務器
startServer();

module.exports = app;
