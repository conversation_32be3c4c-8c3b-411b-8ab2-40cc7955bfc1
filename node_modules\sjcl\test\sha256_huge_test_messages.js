// dd if=/dev/zero bs=1024 count=$((327155712/1024)) | shasum -a 256
sjcl.test.vector.sha256huge =
{
  8388608:   "2daeb1f36095b44b318410b3f4e8b5d989dcc7bb023d1426c492dab0a3053e74", // 8 MB
  16777216:  "080acf35a507ac9849cfcba47dc2ad83e01b75663a516279c8b9d243b719643e", // 16 MB
  33554432:  "83ee47245398adee79bd9c0a8bc57b821e92aba10f5f9ade8a5d1fae4d8c4302", // 32 MB
  67108864:  "3b6a07d0d404fab4e23b6d34bc6696a6a312dd92821332385e5af7c01c421351", // 64 MB
  134217728: "254bcc3fc4f27172636df4bf32de9f107f620d559b20d760197e452b97453917", // 128 MB
  268435456: "a6d72ac7690f53be6ae46ba88506bd97302a093f7108472bd9efc3cefda06484", // 256 MB
  327155712: "365bb7e64d36c629580c71cc026715f4456825db5dbb63ab65688a1fc8613b38"  // 312 MB
};
