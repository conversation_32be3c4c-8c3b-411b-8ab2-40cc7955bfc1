const logger = require('../utils/logger');

// 錯誤處理中間件
function errorHandler(error, req, res, next) {
  logger.error('錯誤處理中間件捕獲錯誤:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
  });

  // 預設錯誤響應
  let statusCode = 500;
  let message = '內部服務器錯誤';

  // 根據錯誤類型設置響應
  if (error.name === 'ValidationError') {
    statusCode = 400;
    message = '數據驗證失敗';
  } else if (error.name === 'CastError') {
    statusCode = 400;
    message = '無效的數據格式';
  } else if (error.code === 11000) {
    statusCode = 409;
    message = '數據已存在';
  } else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = '無效的認證令牌';
  } else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = '認證令牌已過期';
  } else if (error.statusCode) {
    statusCode = error.statusCode;
    message = error.message;
  }

  // 在開發環境中包含錯誤堆棧
  const response = {
    success: false,
    message,
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack }),
  };

  res.status(statusCode).json(response);
}

module.exports = errorHandler;
